import { useState, useEffect } from "react";
import {
  Plus,
  ChevronDown,
  FolderOpen,
  ChevronUp,
  MoreHorizontal,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { usePathname, useRouter } from "@/i18n/navigation";
import { toast } from "sonner";
import FolderDialog from "./FolderDialog";
import DeleteFolderDialog from "./DeleteFolderDialog";
import { folderService } from "@/services/api/folderService";
import { trackEvent } from "@/lib/analytics";

export default function FolderList({
  isAnonymous = false,
  className,
  onFolderChange,
}) {
  const t = useTranslations("dashboard.sidebar");
  const tFolder = useTranslations("folder.list.actions");
  const currentPath = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();

  // 文件夹相关状态
  const [folders, setFolders] = useState([
    { id: "all", name: t("defaultFolderName"), isDefault: true },
  ]);
  const [selectedFolderId, setSelectedFolderId] = useState("all");
  const [isFoldersExpanded, setIsFoldersExpanded] = useState(true);
  const [showCreateFolderDialog, setShowCreateFolderDialog] = useState(false);
  const [showEditFolderDialog, setShowEditFolderDialog] = useState(false);
  const [showDeleteFolderDialog, setShowDeleteFolderDialog] = useState(false);
  const [editingFolder, setEditingFolder] = useState(null);
  const [dropdownOpen, setDropdownOpen] = useState({});
  const [isLoadingFolders, setIsLoadingFolders] = useState(false);

  // 获取文件夹列表
  const fetchFolders = async () => {
    if (isAnonymous) return; // 匿名用户不获取文件夹

    try {
      setIsLoadingFolders(true);
      const response = await folderService.getFolders();
      const { folders: apiFolders } = response.data;

      // 合并默认的"Recent Files"文件夹和API返回的文件夹
      const allFolders = [
        { id: "all", name: t("defaultFolderName"), isDefault: true },
        ...apiFolders,
      ];
      setFolders(allFolders);
    } catch (error) {
      console.error("Error fetching folders:", error);
      // 显示错误提示
      const errorMessage =
        error?.data?.message || error?.message || "Failed to load folders";
      toast.error(errorMessage);
    } finally {
      setIsLoadingFolders(false);
    }
  };

  // 初始化时从URL参数读取选中的文件夹
  useEffect(() => {
    const folderParam = searchParams.get("folder");
    const folderId = folderParam || "all";

    setSelectedFolderId(folderId);

    // 通知父组件当前选中的文件夹，确保状态同步
    if (onFolderChange) {
      onFolderChange(folderId);
    }
  }, [searchParams, onFolderChange]);

  // 组件挂载时获取文件夹列表
  useEffect(() => {
    fetchFolders();
  }, [isAnonymous]);

  // 文件夹相关处理函数
  const handleCreateFolder = async (folderName) => {
    try {
      const response = await folderService.createFolder(folderName);
      const newFolder = response.data;

      // 添加新文件夹到列表
      setFolders((prev) => [...prev, newFolder]);

      // 触发全局事件，通知FileList组件刷新文件夹列表
      window.dispatchEvent(new CustomEvent("folderListChanged"));

      // 自动选中新创建的文件夹
      setSelectedFolderId(newFolder.id);

      // 更新URL参数
      const url = new URL(window.location);
      url.searchParams.set("folder", newFolder.id);
      window.history.pushState({}, "", url);

      // 通知父组件文件夹变化
      if (onFolderChange) {
        onFolderChange(newFolder.id);
      }

      // 返回 true 表示操作成功
      return true;
    } catch (error) {
      console.error("Error creating folder:", error);
      // 显示错误提示，返回 false 表示操作失败
      const errorMessage =
        error?.data?.message || error?.message || "Failed to create folder";
      toast.error(errorMessage);
      return false;
    }
  };

  const handleEditFolder = async (folderId, newName) => {
    try {
      await folderService.updateFolder(folderId, newName);

      // 更新本地状态
      setFolders((prev) =>
        prev.map((folder) =>
          folder.id === folderId ? { ...folder, name: newName } : folder
        )
      );

      // 触发全局事件，通知FileList组件刷新文件夹列表
      window.dispatchEvent(new CustomEvent("folderListChanged"));

      // 通知父组件文件夹发生了变化
      if (onFolderChange && selectedFolderId === folderId) {
        // 如果当前选中的就是被重命名的文件夹，通知父组件刷新
        onFolderChange(folderId);
      }

      // 清理编辑状态
      setEditingFolder(null);

      // 返回 true 表示操作成功
      return true;
    } catch (error) {
      console.error("Error updating folder:", error);
      // 显示错误提示，返回 false 表示操作失败
      const errorMessage =
        error?.data?.message || error?.message || "Failed to update folder";
      toast.error(errorMessage);
      return false;
    }
  };

  const handleDeleteFolder = async (folderId) => {
    try {
      await folderService.deleteFolder(folderId);

      // 从本地状态中移除文件夹
      setFolders((prev) => prev.filter((folder) => folder.id !== folderId));

      // 触发全局事件，通知FileList组件刷新文件夹列表
      window.dispatchEvent(new CustomEvent("folderListChanged"));

      // 如果删除的是当前选中的文件夹，切换到"All Transcriptions"
      if (selectedFolderId === folderId) {
        setSelectedFolderId("all");

        // 更新URL参数
        const url = new URL(window.location);
        url.searchParams.delete("folder");
        window.history.pushState({}, "", url);

        // 通知父组件文件夹变化
        if (onFolderChange) {
          onFolderChange("all");
        }
      } else {
        // 即使删除的不是当前选中的文件夹，也要通知父组件刷新文件夹列表
        if (onFolderChange) {
          onFolderChange(selectedFolderId);
        }
      }

      // 清理删除状态
      setShowDeleteFolderDialog(false);
      setEditingFolder(null);
    } catch (error) {
      console.error("Error deleting folder:", error);
      // 显示错误提示
      const errorMessage =
        error?.data?.message || error?.message || "Failed to delete folder";
      toast.error(errorMessage);
    }
  };

  const handleFolderSelect = (folderId) => {
    setSelectedFolderId(folderId);

    // 检查当前是否在 dashboard 页面
    if (currentPath === "/dashboard") {
      // 在 dashboard 页面，只更新 URL 参数
      const url = new URL(window.location);
      if (folderId === "all") {
        // 如果是默认的"All Transcriptions"，移除folder参数
        url.searchParams.delete("folder");
      } else {
        url.searchParams.set("folder", folderId);
      }
      window.history.pushState({}, "", url);

      // 通知父组件文件夹变化
      if (onFolderChange) {
        onFolderChange(folderId);
      }
    } else {
      // 不在 dashboard 页面，跳转到 dashboard 页面
      if (folderId === "all") {
        // 跳转到 dashboard 主页
        router.push("/dashboard");
      } else {
        // 跳转到 dashboard 页面并带上 folder 参数
        router.push(`/dashboard?folder=${folderId}`);
      }
    }
  };

  const handleOpenEditDialog = (folder) => {
    // 简化状态管理，直接设置状态
    setShowCreateFolderDialog(false);
    setShowDeleteFolderDialog(false);
    setEditingFolder(folder);
    setShowEditFolderDialog(true);
  };

  const handleOpenDeleteDialog = (folder) => {
    // 简化状态管理，直接设置状态
    setShowCreateFolderDialog(false);
    setShowEditFolderDialog(false);
    setEditingFolder(folder);
    setShowDeleteFolderDialog(true);
  };

  // 清理所有对话框状态的函数
  const clearAllDialogStates = () => {
    setShowCreateFolderDialog(false);
    setShowEditFolderDialog(false);
    setShowDeleteFolderDialog(false);
    setEditingFolder(null);
    setDropdownOpen({}); // 清理所有dropdown状态
  };

  // 监听路由变化，根据页面状态管理文件夹展开状态
  useEffect(() => {
    // 默认展开文件夹列表，无论在哪个页面
    // 这样用户从dashboard跳转到其他页面时，Files状态保持展开
    setIsFoldersExpanded(true);
  }, [currentPath]);

  // 清理函数，确保在组件卸载时重置所有对话框状态
  useEffect(() => {
    return () => {
      clearAllDialogStates();
    };
  }, []);

  // 紧急重置机制：如果用户按下Escape键，强制清理所有状态
  useEffect(() => {
    const handleEscapeKey = (event) => {
      if (event.key === "Escape") {
        // 如果有任何对话框打开，清理所有状态
        if (
          showCreateFolderDialog ||
          showEditFolderDialog ||
          showDeleteFolderDialog
        ) {
          clearAllDialogStates();
        }
      }
    };

    document.addEventListener("keydown", handleEscapeKey);
    return () => {
      document.removeEventListener("keydown", handleEscapeKey);
    };
  }, [showCreateFolderDialog, showEditFolderDialog, showDeleteFolderDialog]);

  return (
    <>
      {/* Folders Section - 限制高度并添加滚动 */}
      <div className={cn("space-y-4 px-4 flex flex-col h-full", className)}>
        <div
          className="flex items-center justify-between flex-shrink-0 cursor-pointer hover:bg-gray-50 rounded-lg px-2 py-1 -mx-2 -my-1 transition-colors select-none"
          onClick={() => setIsFoldersExpanded(!isFoldersExpanded)}
        >
          <div className="flex items-center gap-2">
            <FolderOpen className="w-5 h-5 text-gray-500" />
            <span className="font-medium text-sm text-gray-700">
              {t("folders")}
            </span>
          </div>
          <div className="flex gap-1">
            {!isAnonymous && (
              <Button
                size="sm"
                variant="ghost"
                className="w-8 h-8 p-0 text-gray-500 hover:bg-gray-100 rounded-lg"
                onClick={(e) => {
                  e.stopPropagation();
                  // 打点记录创建文件夹按钮点击
                  trackEvent("folder_create_button_clicked");
                  setShowCreateFolderDialog(true);
                }}
              >
                <Plus className="w-4 h-4" />
              </Button>
            )}
            <Button
              size="sm"
              variant="ghost"
              className="w-8 h-8 p-0 text-gray-500 hover:bg-gray-100 rounded-lg"
              onClick={() => setIsFoldersExpanded(!isFoldersExpanded)}
            >
              {isFoldersExpanded ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>

        {isFoldersExpanded && (
          <div className="flex-1 min-h-0 overflow-hidden">
            <div
              className="h-full overflow-y-auto space-y-1 pr-1"
              style={{
                scrollbarWidth: "thin",
                scrollbarColor: "#d1d5db transparent",
              }}
            >
              {/* Loading state */}
              {isLoadingFolders && (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#6366f1]" />
                </div>
              )}

              {/* Folder names with consistent padding */}
              {!isLoadingFolders &&
                folders.map((folder) => (
                  <div
                    key={folder.id}
                    className={`group flex items-center justify-between p-3 pl-7 rounded-xl cursor-pointer transition-all select-none ${
                      selectedFolderId === folder.id
                        ? "bg-[#6366f1]/10 text-[#6366f1]"
                        : "bg-white hover:bg-gray-50 text-gray-600"
                    }`}
                    onClick={() => handleFolderSelect(folder.id)}
                  >
                    <span className="font-medium text-sm truncate flex-1 mr-2">
                      {folder.name}
                    </span>
                    {!folder.isDefault && (
                      <div className="relative">
                        <DropdownMenu
                          open={dropdownOpen[folder.id] || false}
                          onOpenChange={(open) => {
                            setDropdownOpen((prev) => ({
                              ...prev,
                              [folder.id]: open,
                            }));
                          }}
                        >
                          <DropdownMenuTrigger asChild>
                            <Button
                              size="sm"
                              variant="ghost"
                              className={`w-7 h-7 p-0 text-current hover:bg-black/10 rounded-lg transition-opacity focus:outline-none focus:ring-0 focus-visible:ring-0 focus-visible:outline-none active:outline-none ${
                                dropdownOpen[folder.id]
                                  ? "opacity-100"
                                  : "opacity-0 group-hover:opacity-100"
                              }`}
                              style={{ outline: "none", boxShadow: "none" }}
                              onClick={(e) => e.stopPropagation()}
                            >
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem
                              onSelect={() => {
                                // 打点记录重命名文件夹菜单项点击
                                trackEvent("folder_rename_menu_clicked", {
                                  folderId: folder.id,
                                });
                                // 先关闭dropdown
                                setDropdownOpen((prev) => ({
                                  ...prev,
                                  [folder.id]: false,
                                }));
                                // 添加延迟让DropdownMenu完全关闭后再打开Dialog
                                // 这样避免焦点管理冲突
                                setTimeout(() => {
                                  handleOpenEditDialog(folder);
                                }, 100);
                              }}
                            >
                              {tFolder("rename")}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onSelect={() => {
                                // 打点记录删除文件夹菜单项点击
                                trackEvent("folder_delete_menu_clicked", {
                                  folderId: folder.id,
                                });
                                // 先关闭dropdown
                                setDropdownOpen((prev) => ({
                                  ...prev,
                                  [folder.id]: false,
                                }));
                                // 添加延迟让DropdownMenu完全关闭后再打开Dialog
                                // 这样避免焦点管理冲突
                                setTimeout(() => {
                                  handleOpenDeleteDialog(folder);
                                }, 100);
                              }}
                            >
                              {tFolder("delete")}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    )}
                  </div>
                ))}
            </div>
          </div>
        )}
      </div>

      {/* 对话框组件 */}
      <FolderDialog
        isOpen={showCreateFolderDialog}
        onClose={() => setShowCreateFolderDialog(false)}
        onSubmit={handleCreateFolder}
        mode="create"
      />

      <FolderDialog
        isOpen={showEditFolderDialog}
        onClose={() => {
          setShowEditFolderDialog(false);
          setEditingFolder(null);
          // 清理dropdown状态，确保没有残留
          setDropdownOpen({});
        }}
        onSubmit={handleEditFolder}
        folder={editingFolder}
        mode="edit"
      />

      <DeleteFolderDialog
        isOpen={showDeleteFolderDialog}
        onClose={() => {
          setShowDeleteFolderDialog(false);
          setEditingFolder(null);
          // 清理dropdown状态，确保没有残留
          setDropdownOpen({});
        }}
        onDeleteFolder={handleDeleteFolder}
        folder={editingFolder}
      />
    </>
  );
}
