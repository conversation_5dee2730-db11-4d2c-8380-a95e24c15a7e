import { useState, useEffect } from "react";
import { House, FolderOpen } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRouter, usePathname } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";
import { cn } from "@/lib/utils";
import FolderList from "./FolderList";

export default function SidebarMenu({
  className,
  isAnonymous,
  onFolderChange,
}) {
  const t = useTranslations("dashboard.sidebar");
  const router = useRouter();
  const currentPath = usePathname();
  const searchParams = useSearchParams();

  // 处理 Home 点击
  const handleHomeClick = () => {
    // 跳转到 dashboard 主页，和点击 logo 一样的效果
    router.push("/dashboard");
    // 通知父组件重置文件夹选择
    if (onFolderChange) {
      onFolderChange("all");
    }
  };

  if (isAnonymous) {
    return null; // 匿名用户不显示菜单
  }

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Home 菜单项 */}
      <div className="px-4 mb-6">
        <div
          className="flex items-center justify-between flex-shrink-0 cursor-pointer hover:bg-gray-50 rounded-lg px-2 py-1 -mx-2 -my-1 transition-colors select-none"
          onClick={handleHomeClick}
        >
          <div className="flex items-center gap-2">
            <House
              className={`w-5 h-5 ${
                currentPath === "/dashboard" && !searchParams.get("folder")
                  ? "text-[#6366f1]"
                  : "text-gray-500"
              }`}
            />
            <span className="font-medium text-sm text-gray-700">
              {t("home")}
            </span>
          </div>
          {/* 占位 div 保持与 Folders 菜单项相同的高度 */}
          <div className="flex gap-1">
            <div className="w-8 h-8"></div>
            <div className="w-8 h-8"></div>
          </div>
        </div>
      </div>

      {/* FolderList 组件 */}
      <div className="flex-1 min-h-0">
        <FolderList
          isAnonymous={isAnonymous}
          className="h-full"
          onFolderChange={onFolderChange}
        />
      </div>
    </div>
  );
}
